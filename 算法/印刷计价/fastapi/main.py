from fastapi import FastAPI, Form, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import math

app = FastAPI()

app.mount("/static", StaticFiles(directory="static"), name="static")

templates = Jinja2Templates(directory="templates")

def calculate_printing_cost(quantity, setup_cost, intervals, spot_colors=0, white_ink=True, white_ink_setup_cost=0, white_ink_price_per_sheet=0):
    """计算印刷成本，所有价格信息都作为参数传入。"""

    print("--- 成本计算明细 ---")
    print(f"印刷数量：{quantity} 张")
    print(f"专色数量：{spot_colors} 个")
    print(f"开机费：{setup_cost} 元")
    print(f"是否需要白墨：{'是' if white_ink else '否'}")
    if white_ink:
        print(f"白墨开机费：{white_ink_setup_cost:.2f} 元")
        print(f"白墨单价：{white_ink_price_per_sheet:.2f} 元/张")

    # 1. 计算开机费
    total_setup_cost = setup_cost + spot_colors * (setup_cost / 2)
    print(f"开机总费用（含专色）：{setup_cost} + {spot_colors} * ({setup_cost:.2f} / 2) = {total_setup_cost:.2f} 元")

    # 2. 计算四色印刷费用
    total_cmyk_cost = 0
    remaining_quantity = quantity
    prev_end = 0
    print("\n--- 四色印刷费用 ---")
    for i, interval in enumerate(intervals):
        end = interval["end"]
        price = interval["price"]
        interval_quantity = min(remaining_quantity, end - prev_end)
        if interval_quantity < 0:
            interval_quantity = 0

        interval_cost = interval_quantity * price
        total_cmyk_cost += interval_cost

        print(f"区间 {i+1}: 数量 {prev_end+1} - {end} 张，单价 {price:.2f} 元/张，本区间费用：{interval_quantity} * {price:.2f} = {interval_cost:.2f} 元")

        remaining_quantity -= interval_quantity
        prev_end = end

        if remaining_quantity <= 0:
            break
    print(f"四色印刷总费用：{total_cmyk_cost:.2f} 元")

    # 3. 计算专色费用
    total_spot_color_cost = total_cmyk_cost * (spot_colors / 2) if spot_colors > 0 else 0
    print("\n--- 专色印刷费用 ---")
    print(f"专色印刷总费用：{total_cmyk_cost:.2f} * ({spot_colors} / 2) = {total_spot_color_cost:.2f} 元" if spot_colors > 0 else "无专色，专色印刷总费用：0.00 元")

    # 4. 计算白墨印刷费用
    total_white_ink_cost = 0
    if white_ink:
        total_white_ink_cost = white_ink_setup_cost + quantity * white_ink_price_per_sheet
        print("\n--- 白墨印刷费用 ---")
        print(f"白墨印刷总费用：{white_ink_setup_cost:.2f} + {quantity} * {white_ink_price_per_sheet:.2f} = {total_white_ink_cost:.2f} 元")
    else:
        print("\n--- 白墨印刷费用 ---")
        print("无白墨印刷")

    # 5. 计算总价
    total_cost = total_setup_cost + total_cmyk_cost + total_spot_color_cost + total_white_ink_cost

    print("\n--- 总费用 ---")
    print(f"总费用：{total_setup_cost:.2f} (开机费) + {total_cmyk_cost:.2f} (四色) + {total_spot_color_cost:.2f} (专色) + {total_white_ink_cost:.2f} (白墨) = {total_cost:.2f} 元")
    print(f"印刷 {quantity} 张，使用 {spot_colors} 个专色的总价为：{total_cost:.2f} 元\n")

    return total_cost

# 示例配置（现在只包含非价格信息）
price_config = {
    "setup_cost": 270,
    "intervals": [
        {"end": 1000, "price": 0.08},
        {"end": 10000, "price": 0.08},
        {"end": math.inf, "price": 0.07},
    ],
}

white_ink_prices = {
    "setup_cost": 190,
    "price_per_sheet": 0.022
}

@app.get("/", response_class=HTMLResponse)
async def read_root(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/calculate")
async def calculate(request: Request, quantity: int = Form(...), spot_colors: int = Form(0), white_ink: bool = Form(True)):
    total_price = calculate_printing_cost(
        quantity=quantity,
        setup_cost=price_config["setup_cost"],
        intervals=price_config["intervals"],
        spot_colors=spot_colors,
        white_ink=white_ink,
        white_ink_setup_cost=white_ink_prices["setup_cost"],
        white_ink_price_per_sheet=white_ink_prices["price_per_sheet"]
    )
    return templates.TemplateResponse("index.html", {"request": request, "total_price": total_price})
