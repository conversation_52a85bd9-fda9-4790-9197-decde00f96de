#!/Users/<USER>/Documents/code-project/pack相关/算法/印刷计价/fastapi/printing-cost-calculator/.venv/bin/python3
# -*- coding: utf-8 -*-
import sys
from printing_cost_calculator.main import start_server
if __name__ == "__main__":
    if sys.argv[0].endswith("-script.pyw"):
        sys.argv[0] = sys.argv[0][:-11]
    elif sys.argv[0].endswith(".exe"):
        sys.argv[0] = sys.argv[0][:-4]
    sys.exit(start_server())
